import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/services/chat_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/widgets/message_types/message_content_widget.dart';

class ChatDetailScreen extends StatefulWidget {
  final ChatModel chat;

  const ChatDetailScreen({super.key, required this.chat});

  @override
  State<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ChatService _chatService = ChatService();
  final AuthService _authService = AuthService();

  List<ChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isFirstLoad = true;
  String? _errorMessage;
  int _currentUserId = 0;
  int _currentPage = 1;
  static const int _messagesPerPage = 20;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _loadMessages();
    _scrollController.addListener(_onScroll);

    // Ensure scroll to bottom after the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _messages.isNotEmpty) {
          _scrollToBottomInstant();
        }
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        setState(() {
          _currentUserId = user.id;
        });
      }
    } catch (e) {
      print('Error loading current user: $e');
    }
  }

  Future<void> _loadMessages({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _currentPage = 1;
        _isFirstLoad = true; // Reset first load flag on refresh
      });
    }

    try {
      final messages = await _chatService.getChatMessages(
        chatId: widget.chat.id,
        page: _currentPage,
        limit: _messagesPerPage,
      );

      setState(() {
        if (isRefresh) {
          // Reverse messages to show oldest first, newest last
          _messages = messages.reversed.toList();
        } else {
          // For pagination, prepend older messages to the beginning
          _messages.insertAll(0, messages.reversed.toList());
        }
        _isLoading = false;
        _isLoadingMore = false;
      });

      // Scroll to bottom for refresh, initial load, or when adding messages to the end
      if ((isRefresh || _isFirstLoad) && messages.isNotEmpty) {
        // Use a longer delay to ensure ListView is fully built and measured
        Future.delayed(const Duration(milliseconds: 100), () {
          if (isRefresh) {
            _scrollToBottomSmooth(); // Smooth animation for manual refresh
          } else {
            _scrollToBottomInstant(); // Instant scroll for initial load
          }
        });
      }

      // Mark that first load is complete
      if (_isFirstLoad) {
        _isFirstLoad = false;
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  void _onScroll() {
    // Load more older messages when scrolling to the top
    if (_scrollController.position.pixels ==
        _scrollController.position.minScrollExtent) {
      _loadMoreMessages();
    }
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    await _loadMessages();
  }

  void _scrollToBottomInstant() {
    if (_scrollController.hasClients) {
      // For initial load, use jumpTo for instant positioning
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      });
    }
  }

  void _scrollToBottomSmooth() {
    if (_scrollController.hasClients) {
      // For refresh actions, use smooth animation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: FutureBuilder<String>(
          future: widget.chat.chatType == ChatType.private
              ? widget.chat.getPrivateChatDisplayName()
              : Future.value(widget.chat.displayName),
          builder: (context, snapshot) {
            final displayName = snapshot.data ?? widget.chat.displayName;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.chat.chatType.displayName,
                  style: const TextStyle(fontSize: 12, color: Colors.white70),
                ),
              ],
            );
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _loadMessages(isRefresh: true),
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // Show chat info
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.grey[50]!, Colors.grey[100]!],
          ),
        ),
        child: Column(
          children: [
            // Messages List
            Expanded(child: _buildMessagesList()),

            // Message Input (Read-only for now)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(top: BorderSide(color: Colors.grey[200]!)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      enabled: false, // Read-only as per requirements
                      decoration: InputDecoration(
                        hintText:
                            'การส่งข้อความยังไม่พร้อมใช้งาน (อ่านอย่างเดียว)',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  IconButton(
                    onPressed: null, // Disabled as per requirements
                    icon: const Icon(Icons.send),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[400],
                      foregroundColor: Colors.white,
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    if (_isLoading && _messages.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null && _messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'เกิดข้อผิดพลาด',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadMessages(isRefresh: true),
              child: const Text('ลองใหม่'),
            ),
          ],
        ),
      );
    }

    if (_messages.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'ไม่มีข้อความในแชทนี้',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadMessages(isRefresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        itemCount: _messages.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the top for older messages
          if (_isLoadingMore && index == 0) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          // Adjust index if loading indicator is shown at top
          final messageIndex = _isLoadingMore ? index - 1 : index;
          final message = _messages[messageIndex];
          final isMyMessage = message.userId == _currentUserId;

          return _buildMessageBubble(message, isMyMessage);
        },
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, bool isMyMessage) {
    final isTextMessage = message.messageType == MessageType.text;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: isMyMessage
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // User name (only for others, outside balloon)
          if (!isMyMessage && message.user != null)
            Padding(
              padding: EdgeInsets.only(
                left: isTextMessage ? 50 : 50,
                bottom: 4,
              ),
              child: Text(
                message.user!.fullName,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ),

          // Message row with avatar and content
          Row(
            mainAxisAlignment: isMyMessage
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (!isMyMessage) ...[
                CircleAvatar(
                  radius: 18,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    message.user?.firstName.isNotEmpty == true
                        ? message.user!.firstName[0].toUpperCase()
                        : '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],
              // Add flexible space on the left for my messages to push them right
              if (isMyMessage) const Expanded(flex: 1, child: SizedBox()),

              // Message content - with or without balloon based on message type
              Flexible(
                flex: isMyMessage ? 3 : 4,
                child: isTextMessage
                    ? _buildTextMessageBalloon(message, isMyMessage)
                    : _buildMediaMessageContent(message, isMyMessage),
              ),
            ],
          ),

          // Timestamp and read status (outside balloon)
          Padding(
            padding: EdgeInsets.only(
              top: 4,
              left: isMyMessage ? 0 : 50,
              right: isMyMessage ? 12 : 0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: isMyMessage
                  ? MainAxisAlignment.end
                  : MainAxisAlignment.start,
              children: [
                Text(
                  message.timeDisplay,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (isMyMessage) ...[
                  const SizedBox(width: 6),
                  Icon(Icons.done_all, size: 14, color: Colors.grey[500]),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextMessageBalloon(ChatMessage message, bool isMyMessage) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isMyMessage
            ? const Color(0xFF00C851) // Modern green for my messages
            : Colors.white, // White background for others
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: isMyMessage
              ? const Radius.circular(20)
              : const Radius.circular(4),
          bottomRight: isMyMessage
              ? const Radius.circular(4)
              : const Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: MessageContentWidget(message: message, isMyMessage: isMyMessage),
    );
  }

  Widget _buildMediaMessageContent(ChatMessage message, bool isMyMessage) {
    return MessageContentWidget(message: message, isMyMessage: isMyMessage);
  }
}
